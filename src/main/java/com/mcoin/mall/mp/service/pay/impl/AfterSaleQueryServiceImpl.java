package com.mcoin.mall.mp.service.pay.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.mcoin.mall.mp.component.ContextHolder;
import com.mcoin.mall.mp.constant.YesNo;
import com.mcoin.mall.mp.entity.*;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.mapper.*;
import com.mcoin.mall.mp.mapping.AfterSaleMapping;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.pay.AfterSaleDetailRequest;
import com.mcoin.mall.mp.model.pay.AfterSaleDetailResponse;
import com.mcoin.mall.mp.model.pay.AfterSaleGoodsRequest;
import com.mcoin.mall.mp.model.pay.AfterSaleGoodsResponse;
import com.mcoin.mall.mp.service.common.ConfigService;
import com.mcoin.mall.mp.service.common.FileService;
import com.mcoin.mall.mp.service.common.RegionService;
import com.mcoin.mall.mp.service.pay.AfterSaleQueryService;
import com.mcoin.mall.mp.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

import static com.mcoin.mall.mp.constant.AfterSaleConstant.SUB_STATUS_WAIT_BUYER_RETURN;
import static com.mcoin.mall.mp.constant.AfterSaleLogConstant.SELLER_REFUND_ORDER;
import static com.mcoin.mall.mp.constant.AfterSaleLogConstant.getSenceDesc;
import static com.mcoin.mall.mp.util.ConfigUtils.getProperty;
import static java.lang.Integer.parseInt;

@Service
@Slf4j
public class AfterSaleQueryServiceImpl implements AfterSaleQueryService {

    @Resource
    private LsOrderGoodsMapper lsOrderGoodsMapper;

    @Resource
    private LsGoodsMapper lsGoodsMapper;

    @Resource
    private LsGoodsItemMapper lsGoodsItemMapper;

    @Resource
    private LsOrderMapper lsOrderMapper;

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private FileService fileService;

    @Resource
    private LsAfterSaleGoodsMapper lsAfterSaleGoodsMapper;

    @Resource
    private LsAfterSaleMapper lsAfterSaleMapper;

    @Resource
    private ConfigService configService;

    @Resource
    private RegionService regionService;

    @Override
    public AfterSaleGoodsResponse orderGoodsInfo(AfterSaleGoodsRequest request) {
        AfterSaleGoodsResponse response = new AfterSaleGoodsResponse();
        Integer sid = contextHolder.getAuthUserInfo().getSid();
        Integer userId = contextHolder.getAuthUserInfo().getUserId();
        LsOrderGoods orderGoods = this.lsOrderGoodsMapper.selectOne(new LambdaQueryWrapper<LsOrderGoods>()
                .eq(LsOrderGoods::getId, request.getOrderGoodsId())
                .eq(LsOrderGoods::getSid, sid)
                .isNull(LsOrderGoods::getDeleteTime)
                .last("LIMIT 1")
        );

        if (orderGoods != null) {
            LsOrder order = this.lsOrderMapper.selectOne(new LambdaQueryWrapper<LsOrder>()
                    .eq(LsOrder::getId, orderGoods.getOrderId())
                    .eq(LsOrder::getUserId, userId)
                    .eq(LsOrder::getSid, sid)
                    .isNull(LsOrder::getDeleteTime)
                    .last("LIMIT 1")
            );
            if (null == order) {
                throw new BusinessException(Response.Code.PARAM_ILLEGAL, "订单不存在");
            }
            LsGoods lsGoods = this.lsGoodsMapper.selectOne(new LambdaQueryWrapper<LsGoods>()
                    .eq(LsGoods::getId, orderGoods.getGoodsId())
                    .eq(LsGoods::getSid, sid)
                    .isNull(LsGoods::getDeleteTime)
                    .last("LIMIT 1")
            );

            LsGoodsItem goodsItem = this.lsGoodsItemMapper.selectOne(new LambdaQueryWrapper<LsGoodsItem>()
                    .eq(LsGoodsItem::getId, orderGoods.getItemId())
                    .eq(LsGoodsItem::getSid, sid)
                    .isNull(LsGoodsItem::getDeleteTime)
                    .last("LIMIT 1")
            );
            response = AfterSaleMapping.INSTANCE.toOrderGoods(orderGoods, order, lsGoods, goodsItem, objectMapper, fileService, request.getRefundMethod());
        }
        return response;
    }

    @Override
    public AfterSaleDetailResponse detail(AfterSaleDetailRequest request) {
        Integer sid = contextHolder.getAuthUserInfo().getSid();
        Integer userId = contextHolder.getAuthUserInfo().getUserId();
        AfterSaleDetailResponse response = new AfterSaleDetailResponse();
        LsAfterSaleGoods lsAfterSaleGoods = lsAfterSaleGoodsMapper.selectOne(new LambdaQueryWrapper<LsAfterSaleGoods>()
                .eq(LsAfterSaleGoods::getId, request.getId())
                .eq(LsAfterSaleGoods::getSid, sid)
                .isNull(LsAfterSaleGoods::getDeleteTime)
                .last("LIMIT 1")
        );
        if (null == lsAfterSaleGoods) {
            //获取是否有软删除的数据;
            LsAfterSaleGoods delGoods = lsAfterSaleGoodsMapper.selectOne(new LambdaQueryWrapper<LsAfterSaleGoods>()
                    .eq(LsAfterSaleGoods::getId, request.getId())
                    .eq(LsAfterSaleGoods::getSid, sid)
                    .last("LIMIT 1")
            );
            if (delGoods == null) {
                throw new BusinessException(Response.Code.FAIL, "售後申請不存在");
            }
            lsAfterSaleGoods = lsAfterSaleGoodsMapper.selectOne(new LambdaQueryWrapper<LsAfterSaleGoods>()
                    .eq(LsAfterSaleGoods::getOrderGoodsId, delGoods.getOrderGoodsId())
                    .eq(LsAfterSaleGoods::getSid, sid)
                    .isNull(LsAfterSaleGoods::getDeleteTime)
                    .last("LIMIT 1")
            );
        }
        if (null != lsAfterSaleGoods) {
            LsAfterSale lsAfterSale = lsAfterSaleMapper.selectOne(new LambdaQueryWrapper<LsAfterSale>()
                    .eq(LsAfterSale::getId, lsAfterSaleGoods.getAfterSaleId())
                    .eq(LsAfterSale::getSid, sid)
                    .eq(LsAfterSale::getUserId, userId)
                    .isNull(LsAfterSale::getDeleteTime)
                    .last("LIMIT 1")
            );
            if (lsAfterSale == null) {
                throw new BusinessException(Response.Code.FAIL, "售後申請不存在");
            }
            LsOrderGoods lsOrderGoods = lsOrderGoodsMapper.selectOne(new LambdaQueryWrapper<LsOrderGoods>()
                    .eq(LsOrderGoods::getId, lsAfterSaleGoods.getOrderGoodsId())
                    .eq(LsOrderGoods::getSid, sid)
                    .isNull(LsOrderGoods::getDeleteTime)
                    .last("LIMIT 1")
            );
            response = AfterSaleMapping.INSTANCE.toDetail(lsAfterSaleGoods, lsAfterSale, lsOrderGoods, objectMapper, fileService);
            response.setReturnContact(configService.getString("shop", "return_contact", ""));
            response.setReturnContactMobile(configService.getString("shop", "return_contact_mobile", ""));
            Integer province = configService.getInteger("shop", "return_province", 0);
            response.setReturnProvince(province);
            Integer city = configService.getInteger("shop", "return_city", 0);
            response.setReturnCity(city);
            Integer district = configService.getInteger("shop", "return_district", 0);
            response.setReturnDistrict(district);
            String returnAddress = configService.getString("shop", "return_address", "");
            response.setReturnAddress(returnAddress);
            response.setAddress(Joiner.on("").skipNulls()
                    .join(regionService.getRegionName(province),
                            regionService.getRegionName(city),
                            regionService.getRegionName(district), returnAddress));
            Date lastAgreeTime = lsAfterSale.getLastAgreeTime();
            if (lastAgreeTime != null && lsAfterSale.getSubStatus() == SUB_STATUS_WAIT_BUYER_RETURN) {
                // “最后同意售后的时间”+“填写售后物流单号实效”配置 减去 当前系统时间
                int validMinute = parseInt(getProperty("job.afterSale.autoClose.validMinute", "10080"));
                long remainingMs = DateTimeUtil.plusMinuteToDate(lastAgreeTime, validMinute).getTime() - System.currentTimeMillis();
                response.setCloseRemainingSeconds((remainingMs / 1000));
            }

            if (ObjectUtil.equal(lsAfterSale.getRefundReason(), getSenceDesc(SELLER_REFUND_ORDER))){
                response.setFlowShow(YesNo.NO.getValue());
            } else {
                response.setFlowShow(YesNo.YES.getValue());
            }

        }
        return response;
    }
}