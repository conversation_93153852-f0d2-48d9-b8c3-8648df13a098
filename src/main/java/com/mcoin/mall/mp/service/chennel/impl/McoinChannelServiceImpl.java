package com.mcoin.mall.mp.service.chennel.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.mcoin.mall.mp.client.McoinClient;
import com.mcoin.mall.mp.client.model.McoinBalanceRequest;
import com.mcoin.mall.mp.client.model.McoinTokenRequest;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.exception.RetryException;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.service.chennel.McoinChannelService;
import com.mcoin.mall.mp.service.common.CacheService;
import com.mcoin.mall.mp.util.ConfigUtils;
import com.mcoin.mall.mp.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mcoin.mall.mp.util.SentinelUtils.handleBlockException;

@Service
@Slf4j
public class McoinChannelServiceImpl implements McoinChannelService {

    @Resource
    private McoinClient mcoinClient;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CacheService cacheService;

    @Override
    public Integer getPoint(String custId) throws RetryException {
        String tokenCacheKey = cacheService.getMcoinScoreToken(custId);
        RBucket<String> tokenBucket = redissonClient.getBucket(tokenCacheKey);
        String token = tokenBucket.get();
        String appid = ConfigUtils.getProperty("mcoin.appid");
        String privateKey = ConfigUtils.getProperty("mcoin.privatekey");
        String publicKey = ConfigUtils.getProperty("mcoin.publickey");
        if (StringUtils.isBlank(token)) {
            token = getToken(custId);
        }
        McoinBalanceRequest balanceRequest = new McoinBalanceRequest()
                .setAppId(appid)
                .setTimestamp(DateTimeUtil.getStandardTime())
                .setNonceStr(getNonceStr(10))
                .setToken(token)
                .setBody(new McoinBalanceRequest.Body());
        StringBuilder balanceSignSb = new StringBuilder();
        balanceSignSb.append("appId=").append(appid).append("&");
        String body = JSON.toJSONString(balanceRequest.getBody(), SerializerFeature.SortField, SerializerFeature.WriteDateUseDateFormat);
        balanceSignSb.append("body=").append(body).append("&");
        balanceSignSb.append("nonceStr=").append(balanceRequest.getNonceStr()).append("&");
        balanceSignSb.append("timestamp=").append(balanceRequest.getTimestamp()).append("&");
        balanceSignSb.append("token=").append(balanceRequest.getToken());
        log.info("獲取balance加簽前：{}", balanceSignSb);
        String balanceRequestSign;
        try {
            balanceRequestSign = AlipaySignature.rsaSign(balanceSignSb.toString(), privateKey, "UTF-8", "RSA2");
        } catch (AlipayApiException e) {
            throw new BusinessException(Response.Code.FAIL, "加簽失敗");
        }
        balanceRequest.setSign(balanceRequestSign);
        log.info("獲取balance加簽后：{}", balanceRequest);

        String result = handleBlockException(()->mcoinClient.getBalance(balanceRequest), null);
        log.info("mcoin_score獲取balance返回：{}", result);
        if (StringUtils.isNotBlank(result)) {
            //檢查是否token失效，失效重新獲聶
            JSONObject obj = JSON.parseObject(result);
            //token無效,請重新獲取
            if (StringUtils.equals(obj.getString("code"), "9996")) {
                tokenBucket.delete();
                throw new RetryException("token失效重試");
            }
            // 驗證簽名
            verifySign(result, publicKey);
            if (StringUtils.equals(obj.getString("code"), "0000")) {
                JSONObject data = obj.getJSONObject("data");
                String integral = data.getString("integral");
                if (StringUtils.isNotBlank(result)) {
                    return Integer.parseInt(integral);
                }
            }
        }
        return null;
    }

    private String getToken(String custId){
        String tokenCacheKey = cacheService.getMcoinScoreToken(custId);
        RBucket<String> tokenBucket = redissonClient.getBucket(tokenCacheKey);
        String appid = ConfigUtils.getProperty("mcoin.appid");
        String privateKey = ConfigUtils.getProperty("mcoin.privatekey");
        String publicKey = ConfigUtils.getProperty("mcoin.publickey");
        McoinTokenRequest tokenRequest = new McoinTokenRequest()
                .setAppId(appid)
                .setTimestamp(DateTimeUtil.getStandardTime())
                .setNonceStr(getNonceStr(10))
                .setBody(new McoinTokenRequest.Body().setUserId(custId));

        StringBuilder tokenSignSb = new StringBuilder();
        tokenSignSb.append("appId=").append(appid).append("&");
        String body = JSON.toJSONString(tokenRequest.getBody(), SerializerFeature.SortField, SerializerFeature.WriteDateUseDateFormat);
        tokenSignSb.append("body=").append(body).append("&");
        tokenSignSb.append("nonceStr=").append(tokenRequest.getNonceStr()).append("&");
        tokenSignSb.append("timestamp=").append(tokenRequest.getTimestamp());
        log.info("獲取token加簽前：{}", tokenSignSb);
        String tokenRequestSign;
        try {
            tokenRequestSign = AlipaySignature.rsaSign(tokenSignSb.toString(), privateKey, "UTF-8", "RSA2");
        } catch (AlipayApiException e) {
            throw new BusinessException(Response.Code.FAIL, "加簽失敗");
        }
        tokenRequest.setSign(tokenRequestSign);
        log.info("獲取token加簽后：{}", tokenRequest);
        String tokenResponse = handleBlockException(()->mcoinClient.getToken(tokenRequest), null);
        if (StringUtils.isBlank(tokenResponse)) {
            throw new BusinessException(Response.Code.FAIL, "會話時效");
        }
        log.info("mcoin_score獲取token返回：{}", tokenResponse);
        JSONObject obj = JSON.parseObject(tokenResponse);
        if (!StringUtils.equals(obj.getString("code"), "9996") && !StringUtils.equals(obj.getString("code"), "0000")) {
            throw new BusinessException(Response.Code.FAIL, obj.getString("msg"));
        }
        verifySign(tokenResponse, publicKey);
        JSONObject data = (JSONObject)obj.get("data");
        String token = data.getString("token");
        String expires = data.getString("expires");
        long surviveTime = 7200L;
        if (StringUtils.isNotBlank(expires)) {
            surviveTime = Long.parseLong(expires);
        }
        //token放到redis 保存2小時
        tokenBucket.set(token, surviveTime, TimeUnit.SECONDS);
        return token;
    }

    /**
     * 生成随机数，随机串取值范围0~9,a~z,A~Z
     * @param len 随机串长度
     * @return Nonce
     */
    private String getNonceStr(int len) {
        final String dictionary = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

        char[] out=new char[len];
        for(int i=0;i<len;++i){
            int index= RandomUtil.randomInt(62);
            out[i] = dictionary.charAt(index);
        }
        return String.valueOf(out);
    }


    /**
     * 驗簽
     */
    private void verifySign(String params, String publicKey) {
        try {
            if (StringUtils.isBlank(publicKey)) {
                throw new BusinessException(Response.Code.FAIL, "積分服務公鑰未配置");
            }
            JSONObject obj  = JSON.parseObject(params);
            String signStr = obj.getString("sign");
            Map<String, Object> signSourceMap = JSON.parseObject(params, new TypeReference<Map<String, Object>>() {});
            signSourceMap.remove("sign");//這個key不參與驗簽
            if (obj.get("data") != null) {
                String data = JSON.toJSONString(obj.get("data"), SerializerFeature.MapSortField);
                signSourceMap.put("data",data);
            }
            String signSource = MapUtil.sortJoin(signSourceMap, "&", "=", true);
            Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, null, publicKey);
            boolean verify = sign.verify(signSource.getBytes(StandardCharsets.UTF_8), Base64.decode(signStr));
            if (!verify) {
                throw new BusinessException(Response.Code.FAIL, "驗簽失敗");
            }
        } catch (Exception e) {
            throw new BusinessException(Response.Code.FAIL, "驗簽失敗");
        }
    }
}
