package com.mcoin.mall.mp.client.interceptor;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.OffsetTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.mp.constant.ExpressCode;
import com.mcoin.mall.mp.constant.YesNo;
import com.mcoin.mall.mp.entity.LsLogisticsToken;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.mapper.LsLogisticsTokenMapper;
import com.mcoin.mall.mp.model.Response;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JdSignatureInterceptor implements RequestInterceptor {
    private static final String HEX_CHARACTERS = "0123456789ABCDEF";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private LsLogisticsTokenMapper lsLogisticsTokenMapper;

    @Override
    public void apply(RequestTemplate template) {
        // 生成签名逻辑
        String timestamp = DATE_TIME_FORMATTER.format(LocalDateTime.now());

        // 从请求头获取内部参数
        Collection<String> sidHeaders = template.headers().get("X-Internal-Sid");
        String sid = sidHeaders != null ? sidHeaders.iterator().next() : null;
        if (sid == null) {
            throw new BusinessException(Response.Code.FAIL, "参数非法");
        }
        LsLogisticsToken token = lsLogisticsTokenMapper.selectOne(new LambdaQueryWrapper<LsLogisticsToken>()
                        .eq(LsLogisticsToken::getSid, Integer.parseInt(sid))
                        .eq(LsLogisticsToken::getExpressCode, ExpressCode.JD.getCode())
                        .eq(LsLogisticsToken::getDelFlag, YesNo.NO.getValue())
                        .isNull(LsLogisticsToken::getDeleteTime));
        if (token == null) {
            throw new BusinessException(Response.Code.FAIL, "未找到对应的物流令牌");
        }

        String appKey = token.getAppKey();
        String appSecret = token.getAppSecret();
        String accessToken = token.getAccessToken();
  
        String domain = "Tracking_JD";
        String path = template.path();
        String body = new String(template.body(), UTF_8);
        String algorithm = "HMacSHA1";
  
        String content = String.join("", new String[]{
                appSecret,
                "access_token", accessToken,
                "app_key", appKey,
                "method", path,
                "param_json", body,
                "timestamp", timestamp,
                "v", "2.0",
                appSecret});
        String sign;
        try {
            sign = sign(algorithm, content.getBytes(UTF_8), appSecret.getBytes(UTF_8));
            
            // 设置请求参数到template
            template.query("LOP-DN", URLEncoder.encode(domain, UTF_8.name()));
            template.query("access_token", URLEncoder.encode(accessToken, UTF_8.name()));
            template.query("app_key", URLEncoder.encode(appKey, UTF_8.name()));
            template.query("timestamp", URLEncoder.encode(timestamp, UTF_8.name()));
            template.query("v", URLEncoder.encode("2.0", UTF_8.name()));
            template.query("sign", URLEncoder.encode(sign, UTF_8.name()));
            template.query("algorithm", URLEncoder.encode(algorithm, UTF_8.name()));
  
            // 设置请求头
            int offset = OffsetTime.now().getOffset().getTotalSeconds() / 3600;
            template.header("lop-tz", String.valueOf(offset));
            template.header("User-Agent", "lop-http/java");
            template.header("content-type", "application/json;charset=UTF-8");

            // 去除内部请求头
            template.removeHeader("X-Internal-Sid");
        } catch (Exception e) {
            log.error("签名生成失败", e);
            throw new BusinessException(Response.Code.FAIL, "簽名失敗");
        }
    }

    private static String sign(String algorithm, byte[] data, byte[] secret) throws GeneralSecurityException {
        if (Objects.equals(algorithm, "md5-salt")) {
            return bytesToHex(MessageDigest.getInstance("md5").digest(data));
        } else if (Objects.equals(algorithm, "HMacMD5")) {
            Mac mac = Mac.getInstance(algorithm);
            mac.init(new SecretKeySpec(secret, algorithm));
            return Base64.getEncoder().encodeToString(mac.doFinal(data));
        } else if (Objects.equals(algorithm, "HMacSHA1")) {
            Mac mac = Mac.getInstance(algorithm);
            mac.init(new SecretKeySpec(secret, algorithm));
            return Base64.getEncoder().encodeToString(mac.doFinal(data));
        } else if (Objects.equals(algorithm, "HMacSHA256")) {
            Mac mac = Mac.getInstance(algorithm);
            mac.init(new SecretKeySpec(secret, algorithm));
            return Base64.getEncoder().encodeToString(mac.doFinal(data));
        } else if (Objects.equals(algorithm, "HMacSHA512")) {
            Mac mac = Mac.getInstance(algorithm);
            mac.init(new SecretKeySpec(secret, algorithm));
            return Base64.getEncoder().encodeToString(mac.doFinal(data));
        }
        throw new GeneralSecurityException("Algorithm " + algorithm + " not supported yet");
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder stringBuilder = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            stringBuilder.append(HEX_CHARACTERS.charAt((b >>> 4) & 0x0F));
            stringBuilder.append(HEX_CHARACTERS.charAt(b & 0x0F));
        }
        return stringBuilder.toString();
    }

    public static String httpBuildQuery(Map<String, String> query) throws UnsupportedEncodingException {
        StringBuilder stringBuilder = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : query.entrySet()) {
            if (!first) {
                stringBuilder.append("&");
            } else {
                first = false;
            }
            stringBuilder.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), UTF_8.name()));
        }
        return stringBuilder.toString();
    }

    public static byte[] readAllBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int n;
        while ((n = inputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, n);
        }
        return outputStream.toByteArray();
    }
}